import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/text_style.dart';
import '../../../../reusableWidgets/connection_error/network_aware_tap.dart';

/// Reusable form widget for editing profile information
/// Handles form validation and user input with skeleton loading states
class EditProfileForm extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController handleController;
  final TextEditingController nameController;
  final TextEditingController locationController;
  final TextEditingController bioController;
  final String? clubInvitation;
  final bool isLoading;
  final Function(String?) onClubInvitationChanged;
  final Function() onNameChanged;
  final Function() onLocationChanged;
  final Function() onBioChanged;

  const EditProfileForm({
    super.key,
    required this.formKey,
    required this.handleController,
    required this.nameController,
    required this.locationController,
    required this.bioController,
    required this.clubInvitation,
    required this.isLoading,
    required this.onClubInvitationChanged,
    required this.onNameChanged,
    required this.onLocationChanged,
    required this.onBioChanged,
  });

  @override
  State<EditProfileForm> createState() => _EditProfileFormState();
}

class _EditProfileFormState extends State<EditProfileForm> {
  bool nameValidation = false;
  bool locationValidation = false;
  bool bioValidation = false;

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: widget.isLoading,
      child: Form(
        key: widget.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Handle field (read-only)
            _buildHandleField(),
            const SizedBox(height: 25),
            
            // Name field
            _buildNameField(),
            nameValidation ? const SizedBox(height: 15) : const SizedBox.shrink(),
            const SizedBox(height: 25),
            
            // Location field
            _buildLocationField(),
            locationValidation ? const SizedBox(height: 15) : const SizedBox.shrink(),
            const SizedBox(height: 25),
            
            // Bio field
            _buildBioField(),
            const SizedBox(height: 25),
            
            // Club invitation section
            _buildClubInvitationSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHandleField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Handle:',
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        const SizedBox(height: 10),
        TextFormField(
          controller: widget.handleController,
          style: lbRegular.copyWith(fontSize: 18),
          enabled: false,
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color.fromRGBO(255, 255, 255, 1),
            contentPadding: const EdgeInsets.all(10),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(10),
              borderSide: const BorderSide(
                color: AppConstants.primaryColor,
                width: 1.5,
              ),
            ),
          ),
          readOnly: true,
        ),
      ],
    );
  }

  Widget _buildNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Name:',
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        const SizedBox(height: 10),
        Stack(
          clipBehavior: Clip.none,
          children: [
            TextFormField(
              controller: widget.nameController,
              textCapitalization: TextCapitalization.sentences,
              style: lbRegular.copyWith(fontSize: 18),
              maxLines: null,
              decoration: InputDecoration(
                suffixIcon: GestureDetector(
                  onTap: () {},
                  child: Image.asset(
                    'assets/icons/Edit.png',
                    filterQuality: FilterQuality.high,
                    fit: BoxFit.cover,
                    height: 38,
                    width: 38,
                  ),
                ),
                filled: true,
                fillColor: const Color.fromRGBO(255, 255, 255, 1),
                contentPadding: const EdgeInsets.all(10),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5),
                  borderSide: const BorderSide(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  nameValidation = false;
                });
                widget.onNameChanged();
              },
              validator: (value) {
                nameValidation = false;
                if (value!.isEmpty) {
                  setState(() {
                    nameValidation = true;
                  });
                  return null;
                } else {
                  return null;
                }
              },
            ),
            Positioned(
              left: 0,
              right: 0,
              top: 55,
              child: nameValidation
                  ? Text(
                      '*Enter name',
                      overflow: TextOverflow.clip,
                      style: lbBold.copyWith(
                        fontSize: 14,
                        color: AppConstants.redColor,
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLocationField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Location:',
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        const SizedBox(height: 10),
        Stack(
          clipBehavior: Clip.none,
          children: [
            TextFormField(
              controller: widget.locationController,
              textCapitalization: TextCapitalization.sentences,
              style: lbRegular.copyWith(fontSize: 18),
              maxLength: 30,
              maxLines: null,
              decoration: InputDecoration(
                counterStyle: lbRegular.copyWith(fontSize: 14),
                suffixIcon: NetworkAwareTap(
                  onTap: () {},
                  child: Image.asset(
                    'assets/icons/Edit.png',
                    filterQuality: FilterQuality.high,
                    fit: BoxFit.cover,
                    height: 38,
                    width: 38,
                  ),
                ),
                filled: true,
                fillColor: const Color.fromRGBO(255, 255, 255, 1),
                contentPadding: const EdgeInsets.all(10),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5),
                  borderSide: const BorderSide(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  locationValidation = false;
                });
                widget.onLocationChanged();
              },
              validator: (value) {
                locationValidation = false;
                if (value!.isEmpty) {
                  setState(() {
                    locationValidation = true;
                  });
                  return null;
                } else {
                  return null;
                }
              },
            ),
            Positioned(
              left: 0,
              right: 0,
              top: 55,
              child: widget.locationController.text.isEmpty
                  ? Text(
                      '*Enter location',
                      overflow: TextOverflow.clip,
                      style: lbBold.copyWith(
                        fontSize: 14,
                        color: AppConstants.redColor,
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBioField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Bio (150 Characters max):',
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        const SizedBox(height: 10),
        Stack(
          clipBehavior: Clip.none,
          children: [
            TextFormField(
              maxLength: 150,
              controller: widget.bioController,
              textCapitalization: TextCapitalization.sentences,
              style: lbRegular.copyWith(fontSize: 18),
              maxLines: null,
              decoration: InputDecoration(
                counterStyle: lbRegular.copyWith(
                  fontSize: 14,
                  color: AppConstants.primaryColor,
                ),
                suffixIcon: NetworkAwareTap(
                  onTap: () {},
                  child: Image.asset(
                    'assets/icons/Edit.png',
                    filterQuality: FilterQuality.high,
                    fit: BoxFit.cover,
                    height: 38,
                    width: 38,
                  ),
                ),
                filled: true,
                fillColor: const Color.fromRGBO(255, 255, 255, 1),
                contentPadding: const EdgeInsets.all(10),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5),
                  borderSide: const BorderSide(
                    color: AppConstants.primaryColor,
                    width: 1.5,
                  ),
                ),
              ),
              onChanged: (value) {
                setState(() {
                  bioValidation = false;
                });
                widget.onBioChanged();
              },
              validator: (value) {
                bioValidation = false;
                if (value!.isEmpty) {
                  setState(() {
                    bioValidation = true;
                  });
                  return null;
                } else {
                  return null;
                }
              },
            ),
            Positioned(
              left: 0,
              right: 0,
              top: 55,
              child: widget.bioController.text.isEmpty
                  ? Text(
                      '*Enter bio',
                      overflow: TextOverflow.clip,
                      style: lbBold.copyWith(
                        fontSize: 14,
                        color: AppConstants.redColor,
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildClubInvitationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Open to club invitations ?',
          overflow: TextOverflow.ellipsis,
          style: lbRegular.copyWith(fontSize: 18),
        ),
        const SizedBox(height: 3),
        Text(
          '(Must be set to "Yes" to receive new club invitations)',
          overflow: TextOverflow.ellipsis,
          style: lbItalic.copyWith(fontSize: 14),
        ),
        const SizedBox(height: 10),
        Row(
          children: [
            SizedBox(
              height: 19,
              width: 19,
              child: Radio<String>(
                fillColor: const WidgetStatePropertyAll(AppConstants.primaryColor),
                value: 'Yes',
                groupValue: widget.clubInvitation,
                onChanged: widget.onClubInvitationChanged,
              ),
            ),
            const SizedBox(width: 15),
            Text('Yes', style: lbRegular.copyWith(fontSize: 18)),
            const SizedBox(width: 25),
            SizedBox(
              height: 19,
              width: 19,
              child: Radio<String>(
                fillColor: const WidgetStatePropertyAll(AppConstants.primaryColor),
                value: 'No',
                groupValue: widget.clubInvitation,
                onChanged: widget.onClubInvitationChanged,
              ),
            ),
            const SizedBox(width: 15),
            Text('No', style: lbRegular.copyWith(fontSize: 18)),
          ],
        ),
      ],
    );
  }
}
