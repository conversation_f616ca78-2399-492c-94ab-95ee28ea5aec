import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../constants/constants.dart';
import '../../../../constants/text_style.dart';
import '../../../../reusableWidgets/imageBuilder.dart';

/// Profile photo editor widget with skeleton loading states
/// Handles profile picture display and editing functionality
class ProfilePhotoEditor extends StatelessWidget {
  final String userProfilePicture;
  final bool isImageLoading;
  final String imageValidation;
  final VoidCallback onTap;
  final bool isDataLoading;

  const ProfilePhotoEditor({
    super.key,
    required this.userProfilePicture,
    required this.isImageLoading,
    required this.imageValidation,
    required this.onTap,
    required this.isDataLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Profile photo editor tile
        Skeleton.replace(
          replacement: _buildSkeletonTile(),
          child: _buildProfileTile(),
        ),

        // Image validation error message
        if (imageValidation.isNotEmpty) ...[
          const SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Row(
              children: [
                Text(
                  imageValidation,
                  overflow: TextOverflow.clip,
                  style: lbBold.copyWith(
                    fontSize: 14,
                    color: AppConstants.redColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildProfileTile() {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        vertical: 1.5,
        horizontal: 16,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      dense: true,
      tileColor: Colors.transparent,
      title: Text(
        'Edit Profile Photo',
        overflow: TextOverflow.ellipsis,
        style: lbRegular.copyWith(fontSize: 18),
      ),
      leading: _buildProfileImage(),
      onTap: onTap,
    );
  }

  Widget _buildSkeletonTile() {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        vertical: 1.5,
        horizontal: 16,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: const BorderSide(
          color: AppConstants.primaryColor,
          width: 1.5,
        ),
      ),
      dense: true,
      tileColor: Colors.transparent,
      title: Text(
        'Edit Profile Photo',
        overflow: TextOverflow.ellipsis,
        style: lbRegular.copyWith(fontSize: 18),
      ),
      leading: _buildSkeletonImage(),
    );
  }

  Widget _buildProfileImage() {
    if (isImageLoading) {
      return _buildLoadingImage();
    }

    return ClipRRect(
      clipBehavior: ui.Clip.none,
      borderRadius: BorderRadius.circular(50),
      child: CustomCachedNetworkImage(
        errorImage: AppConstants.profileLogoImagePath,
        height: 45,
        width: 45,
        imageUrl: userProfilePicture,
      ),
    );
  }

  Widget _buildSkeletonImage() {
    return ClipRRect(
      clipBehavior: ui.Clip.none,
      borderRadius: BorderRadius.circular(50),
      child: Container(
        height: 45,
        width: 45,
        decoration: BoxDecoration(
          color: AppConstants.skeletonforgroundColor,
          borderRadius: BorderRadius.circular(50),
        ),
      ),
    );
  }

  Widget _buildLoadingImage() {
    return Container(
      height: 45,
      width: 45,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(50),
      ),
      child: const Center(
        child: SizedBox(
          height: 20,
          width: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation(AppConstants.primaryColor),
          ),
        ),
      ),
    );
  }
}

/// Skeleton version for loading states
class ProfilePhotoEditorSkeleton extends StatelessWidget {
  const ProfilePhotoEditorSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          contentPadding: const EdgeInsets.symmetric(
            vertical: 1.5,
            horizontal: 16,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
            side: const BorderSide(
              color: AppConstants.primaryColor,
              width: 1.5,
            ),
          ),
          dense: true,
          tileColor: Colors.transparent,
          title: Text(
            'Loading...',
            overflow: TextOverflow.ellipsis,
            style: lbRegular.copyWith(fontSize: 18),
          ),
          leading: Container(
            height: 45,
            width: 45,
            decoration: BoxDecoration(
              color: AppConstants.skeletonforgroundColor,
              borderRadius: BorderRadius.circular(50),
            ),
          ),
        ),
      ],
    );
  }
}

/// Image upload progress indicator
class ImageUploadProgress extends StatelessWidget {
  final double? progress;
  final String message;

  const ImageUploadProgress({
    super.key,
    this.progress,
    this.message = 'Uploading image...',
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (progress != null)
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[300],
              valueColor:
                  const AlwaysStoppedAnimation(AppConstants.primaryColor),
            )
          else
            const LinearProgressIndicator(
              backgroundColor: Colors.grey,
              valueColor: AlwaysStoppedAnimation(AppConstants.primaryColor),
            ),
          const SizedBox(height: 10),
          Text(
            message,
            style: lbRegular.copyWith(fontSize: 14),
          ),
        ],
      ),
    );
  }
}

/// Error display for image upload failures
class ImageUploadError extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;

  const ImageUploadError({
    super.key,
    required this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppConstants.redColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(color: AppConstants.redColor),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.error_outline,
            color: AppConstants.redColor,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: lbRegular.copyWith(
              fontSize: 14,
              color: AppConstants.redColor,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 10),
            TextButton(
              onPressed: onRetry,
              child: Text(
                'Retry',
                style: lbBold.copyWith(
                  fontSize: 14,
                  color: AppConstants.primaryColor,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
