import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../../../constants/config.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/common_helper.dart';
import '../../../../constants/text_style.dart';
import '../../../../controller/user_controller.dart';
import '../../../../reusableWidgets/imageBuilder.dart';

/// Reusable profile photo editor widget with loading states
/// Handles image picking, cropping, validation, and upload
class ProfilePhotoEditor extends StatefulWidget {
  final String userProfilePicture;
  final int? userId;
  final Function(String) onImageUpdated;
  final Function(String) onValidationError;

  const ProfilePhotoEditor({
    super.key,
    required this.userProfilePicture,
    required this.userId,
    required this.onImageUpdated,
    required this.onValidationError,
  });

  @override
  State<ProfilePhotoEditor> createState() => _ProfilePhotoEditorState();
}

class _ProfilePhotoEditorState extends State<ProfilePhotoEditor> {
  bool isImageLoading = false;
  File? _image;
  String imageValidation = '';

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Skeletonizer(
          enabled: isImageLoading,
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(
              vertical: 1.5,
              horizontal: 16,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
              side: const BorderSide(
                color: AppConstants.primaryColor,
                width: 1.5,
              ),
            ),
            dense: true,
            tileColor: Colors.transparent,
            title: Text(
              'Edit Profile Photo',
              overflow: TextOverflow.ellipsis,
              style: lbRegular.copyWith(fontSize: 18),
            ),
            leading: ClipRRect(
              clipBehavior: ui.Clip.none,
              borderRadius: BorderRadius.circular(50),
              child: CustomCachedNetworkImage(
                errorImage: AppConstants.profileLogoImagePath,
                height: 45,
                width: 45,
                imageUrl: widget.userProfilePicture,
              ),
            ),
            onTap: isImageLoading ? null : _pickImage,
          ),
        ),
        
        // Validation error message
        if (imageValidation.isNotEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20.0),
            child: Row(
              children: [
                Text(
                  imageValidation,
                  overflow: TextOverflow.clip,
                  style: lbBold.copyWith(
                    fontSize: 14,
                    color: AppConstants.redColor,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Future<void> _pickImage() async {
    setState(() {
      isImageLoading = true;
      imageValidation = '';
    });
    widget.onValidationError('');

    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 100,
      maxHeight: 1000,
      maxWidth: 1000,
    );

    if (pickedFile != null) {
      final fileSize = await File(pickedFile.path).length();
      const maxSizeInBytes = 10 * 1024 * 1024;

      if (fileSize > maxSizeInBytes) {
        setState(() {
          isImageLoading = false;
          imageValidation = 'Selected image exceeds the maximum allowed size of 10 MB.';
        });
        widget.onValidationError(imageValidation);
        return;
      }

      // Validate file type (MIME type)
      final mimeType = CommonHelper.getMimeType(pickedFile.path);
      const allowedMimeTypes = ['image/jpeg', 'image/png'];

      if (mimeType == null || !allowedMimeTypes.contains(mimeType)) {
        setState(() {
          isImageLoading = false;
          imageValidation = 'Selected file is not a valid image type (JPEG/PNG).';
        });
        widget.onValidationError(imageValidation);
        return;
      }

      final file = await _cropUserImage(pickedFile);
      if (file != null) {
        final croppedImage = await _convertSquareImageToCircle(File(file.path));
        if (croppedImage.path.isNotEmpty) {
          final renamedCroppedImage =
              await CommonHelper.renameFile(File(pickedFile.path), croppedImage);
          setState(() {
            _image = renamedCroppedImage;
          });

          try {
            if (mounted) {
              final responseMap =
                  await Provider.of<UserController>(context, listen: false)
                      .uploadFile(_image!, widget.userId ?? 0, context);
              if (responseMap.containsKey('error')) {
                log(responseMap['error']);
                setState(() {
                  imageValidation = 'Failed to upload image. Please try again.';
                });
                widget.onValidationError(imageValidation);
              } else {
                final newImageUrl = Config.imageBaseUrl + responseMap["data"];
                widget.onImageUpdated(newImageUrl);
              }
            }
          } catch (e) {
            log('Error uploading image: $e');
            setState(() {
              imageValidation = 'Failed to upload image. Please try again.';
            });
            widget.onValidationError(imageValidation);
          }
        }
      }
    } else {
      log('Image not picked');
      setState(() {
        _image = null;
      });
    }

    setState(() {
      isImageLoading = false;
    });
  }

  Future<File> _convertSquareImageToCircle(File imageFile) async {
    final image = await decodeImageFromList(await imageFile.readAsBytes());
    final output = await _createCircleImage(image);
    final file = File(imageFile.path);
    await file.writeAsBytes(output);
    return file;
  }

  Future<Uint8List> _createCircleImage(ui.Image image) async {
    final pictureRecorder = ui.PictureRecorder();
    final canvas = Canvas(pictureRecorder);

    final size = image.width;
    final radius = size / 2;

    // Draw the circle
    final paint = Paint();
    paint.color = Colors.transparent;
    canvas.drawCircle(Offset(radius, radius), radius, paint);

    // Clip the image to the circle
    canvas.clipPath(
      Path()
        ..addOval(
          Rect.fromCircle(
            center: Offset(radius, radius),
            radius: radius,
          ),
        ),
    );

    // Draw the image centered in the circle
    canvas.drawImage(image, const Offset(0, 0), Paint());

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size, size);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  Future<CroppedFile?> _cropUserImage(XFile pickedFile) async {
    final croppedImage = await ImageCropper().cropImage(
      sourcePath: pickedFile.path,
      aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
      compressFormat: ImageCompressFormat.png,
      compressQuality: 100,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop your image',
          toolbarColor: AppConstants.primaryColor,
          cropFrameColor: AppConstants.primaryColor,
          toolbarWidgetColor: Colors.white,
          cropStyle: CropStyle.circle,
          hideBottomControls: true,
          lockAspectRatio: true,
          showCropGrid: true,
        ),
        IOSUiSettings(
          minimumAspectRatio: 1,
          title: 'Crop your image',
          doneButtonTitle: 'Save',
          cancelButtonTitle: 'Cancel',
          cropStyle: CropStyle.circle,
          showCancelConfirmationDialog: true,
          hidesNavigationBar: false,
          rotateButtonsHidden: true,
          rotateClockwiseButtonHidden: true,
          aspectRatioPickerButtonHidden: true,
          aspectRatioLockEnabled: true,
        ),
      ],
    );
    return croppedImage;
  }
}
