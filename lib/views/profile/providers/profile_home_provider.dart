import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../constants/common_helper.dart';
import '../../../constants/constants.dart';
import '../../../controller/book_case_controller.dart';
import '../../../controller/user_controller.dart';
import '../../../models/book_case_model.dart';
import '../../../models/book_club_model.dart';
import '../../../models/user_model.dart';
import '../../../reusable_api_function/club/club_function.dart';
import '../models/profile_screen_state.dart';
import '../services/profile_sync_service.dart';

/// Dedicated provider for Profile Home Screen
/// Implements targeted refresh methods and cross-screen state synchronization
class ProfileHomeProvider with ChangeNotifier {
  // Dependencies
  BookCaseController? _bookCaseController;
  UserController? _userController;
  ClubController? _clubController;

  // Sync service for cross-screen communication
  final ProfileSyncService _syncService = ProfileSyncService();
  StreamSubscription<ProfileSyncEvent>? _syncSubscription;
  StreamSubscription<bool>? _refreshSubscription;

  // Screen state
  final ProfileScreenState _state = ProfileScreenState();

  // Getters for state
  ProfileScreenState get state => _state;
  int? get loggedInUserId => _state.loggedInUserId;
  String? get userName => _state.userName;
  String? get userLocation => _state.userLocation;
  String? get userHandler => _state.userHandler;
  String? get userBio => _state.userBio;
  UserModel get userModel => _state.userModel;
  bool get isLoading => _state.isLoading;
  bool get isUserLoading => _state.isUserLoading;

  // Bookcase getters
  List<BookCaseModel>? get currentBookCaseList => _state.currentBookCaseList;
  List<BookCaseModel>? get topShelfList => _state.topShelfList;
  List<BookCaseModel>? get completedBooks => _state.completedBooks;
  List<BookCaseModel>? get toBeReadList => _state.toBeReadList;
  List<BookCaseModel>? get bookCase => _state.bookCase;

  // Club getters
  List<BookClubModel>? get standingBookClubList => _state.standingBookClubList;
  List<BookClubModel>? get impromptuBookClubList =>
      _state.impromptuBookClubList;

  // Loading state getters
  bool get currentReadLoading => _state.currentReadLoading;
  bool get topShelfLoading => _state.topShelfLoading;
  bool get toBeReadLoading => _state.toBeReadLoading;
  bool get standingClubLoading => _state.standingClubLoading;

  // Pagination getters
  int get currentReadCount => _state.currentReadCount;
  int get topShelfCount => _state.topShelfCount;
  int get toBeReadCount => _state.toBeReadCount;
  int get standingClubCount => _state.standingClubCount;

  /// Initialize provider with context
  void initialize(BuildContext context) {
    _bookCaseController =
        Provider.of<BookCaseController>(context, listen: false);
    _userController = Provider.of<UserController>(context, listen: false);
    _clubController = Provider.of<ClubController>(context, listen: false);

    _initializeSyncListeners();
    log('ProfileHomeProvider: Initialized');
  }

  /// Initialize sync listeners
  void _initializeSyncListeners() {
    _syncSubscription = _syncService.syncStream.listen(_handleSyncEvent);
    _refreshSubscription =
        _syncService.refreshStream.listen(_handleRefreshRequest);
  }

  /// Handle sync events from other screens
  void _handleSyncEvent(ProfileSyncEvent event) {
    log('ProfileHomeProvider: Handling sync event - ${event.type}');

    switch (event.type) {
      case ProfileSyncEventType.profileUpdated:
        _handleProfileUpdated(event.data);
        break;
      case ProfileSyncEventType.bookcaseChanged:
        _handleBookcaseChanged(event.data);
        break;
      case ProfileSyncEventType.currentReadingUpdated:
        _handleCurrentReadingUpdated(event.data);
        break;
      case ProfileSyncEventType.toBeReadUpdated:
        _handleToBeReadUpdated(event.data);
        break;
      case ProfileSyncEventType.completedBooksUpdated:
        _handleCompletedBooksUpdated(event.data);
        break;
      case ProfileSyncEventType.clubMembershipChanged:
        _handleClubMembershipChanged(event.data);
        break;
      case ProfileSyncEventType.generalRefresh:
        refreshAllData();
        break;
    }
  }

  /// Handle refresh requests
  void _handleRefreshRequest(bool shouldRefresh) {
    if (shouldRefresh) {
      log('ProfileHomeProvider: Handling refresh request');
      refreshAllData();
    }
  }

  /// Handle profile updated event
  void _handleProfileUpdated(Map<String, dynamic>? data) {
    if (data != null) {
      // Update user data from sync event
      _state.updateUserData(
        name: data['userName'],
        location: data['userLocation'],
        bio: data['userBio'],
      );
      notifyListeners();
    }
  }

  /// Handle bookcase changed event
  void _handleBookcaseChanged(Map<String, dynamic>? data) {
    if (data != null) {
      final bookcaseType = data['bookcaseType'] as String?;
      final books = data['books'] as List<dynamic>?;

      if (bookcaseType != null && books != null) {
        final bookList = books.cast<BookCaseModel>();

        switch (bookcaseType) {
          case 'currentReading':
            _state.updateBookcaseData(currentReading: bookList);
            break;
          case 'topShelf':
            _state.updateBookcaseData(topShelf: bookList);
            break;
          case 'completed':
            _state.updateBookcaseData(completed: bookList);
            break;
          case 'toBeRead':
            _state.updateBookcaseData(toBeRead: bookList);
            break;
        }
        notifyListeners();
      }
    }
  }

  /// Handle current reading updated event
  void _handleCurrentReadingUpdated(Map<String, dynamic>? data) {
    if (data != null) {
      final books = data['books'] as List<dynamic>?;
      if (books != null) {
        _state.updateBookcaseData(currentReading: books.cast<BookCaseModel>());
        notifyListeners();
      }
    }
  }

  /// Handle to-be-read updated event
  void _handleToBeReadUpdated(Map<String, dynamic>? data) {
    if (data != null) {
      final books = data['books'] as List<dynamic>?;
      if (books != null) {
        _state.updateBookcaseData(toBeRead: books.cast<BookCaseModel>());
        notifyListeners();
      }
    }
  }

  /// Handle completed books updated event
  void _handleCompletedBooksUpdated(Map<String, dynamic>? data) {
    if (data != null) {
      final books = data['books'] as List<dynamic>?;
      if (books != null) {
        _state.updateBookcaseData(completed: books.cast<BookCaseModel>());
        notifyListeners();
      }
    }
  }

  /// Handle club membership changed event
  void _handleClubMembershipChanged(Map<String, dynamic>? data) {
    if (data != null) {
      final clubType = data['clubType'] as String?;
      final clubs = data['clubs'] as List<dynamic>?;

      if (clubType != null && clubs != null) {
        final clubList = clubs.cast<BookClubModel>();

        switch (clubType) {
          case 'standing':
            _state.updateClubData(standing: clubList);
            break;
          case 'impromptu':
            _state.updateClubData(impromptu: clubList);
            break;
        }
        notifyListeners();
      }
    }
  }

  /// Refresh all data
  Future<void> refreshAllData([BuildContext? context]) async {
    log('ProfileHomeProvider: Refreshing all data');
    _state.updateLoadingStates(general: true);
    notifyListeners();

    try {
      await _initializeUserId();
      if (context != null) {
        await getUserDetails(context);
        await Future.wait([
          getCurrentReadBookCase(false, context),
          getToBeReadBook(false, context),
          getTopShelfBookCase(false, context),
          getStandingBookClubsByUserId(false, context),
          getImpromptuBookClubsByUserId(false, context),
        ]);
      }
    } catch (e) {
      log('ProfileHomeProvider: Error refreshing data - $e');
    } finally {
      _state.updateLoadingStates(general: false);
      notifyListeners();
    }
  }

  /// Initialize user ID and basic data
  Future<void> _initializeUserId() async {
    final userName = await CommonHelper.getLoggedinUserName();
    final userLocation = await CommonHelper.getLoggedinUserLocation();
    final userHandler = await CommonHelper.getLoggedinUserHandler();
    final loggedInUserId = await CommonHelper.getLoggedInUserId();
    final userBio = await CommonHelper.getLoggedinUserBio();

    _state.updateUserData(
      userId: loggedInUserId,
      name: userName,
      location: userLocation,
      handler: userHandler,
      bio: userBio,
    );
  }

  /// Get user details
  Future<void> getUserDetails([BuildContext? context]) async {
    if (_userController == null || _state.loggedInUserId == null) return;

    try {
      _state.updateLoadingStates(user: true);
      notifyListeners();

      await _userController!
          .getUserDetailsByUserId(_state.loggedInUserId!, context!);
      _state.updateUserData(model: _userController!.userModel);
    } catch (e) {
      log('ProfileHomeProvider: Error getting user details - $e');
    } finally {
      _state.updateLoadingStates(user: false);
      notifyListeners();
    }
  }

  /// Get current reading bookcase
  Future<void> getCurrentReadBookCase(bool isMore,
      [BuildContext? context]) async {
    if (_bookCaseController == null || _state.loggedInUserId == null) return;

    if ((_state.currentBookCaseList?.length ?? 0) <= _state.currentReadCount ||
        !isMore) {
      _state.updateLoadingStates(currentRead: true);
      notifyListeners();

      if (isMore) {
        _state.currentReadLimit += 10;
      }
    }

    try {
      final responseMap = await _bookCaseController!.getCurrentReadBookCase(
        _state.loggedInUserId!,
        _state.currentReadLimit,
        _state.offset,
        context!,
      );

      if (responseMap["statusCode"] == 200) {
        List<BookCaseModel> bookList = [];
        _state.updateCounts(currentRead: responseMap['count']);
        bookList = (responseMap["data"] as List)
            .map((item) => BookCaseModel.fromJson(item))
            .toList();

        var result = CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
        if (result.isNotEmpty) {
          _state.updateBookcaseData(currentReading: result[0]);
        }

        if ((_state.currentBookCaseList?.length ?? 0) >=
            _state.currentReadCount) {
          _state.updateLoadingStates(currentRead: false);
        }
      }
    } catch (e) {
      log('ProfileHomeProvider: Error getting current read bookcase - $e');
    } finally {
      _state.updateLoadingStates(currentRead: false);
      notifyListeners();
    }
  }

  /// Get top shelf bookcase
  Future<void> getTopShelfBookCase(bool isMore, [BuildContext? context]) async {
    if (_bookCaseController == null || _state.loggedInUserId == null) return;

    if ((_state.topShelfList?.length ?? 0) <= _state.topShelfCount || !isMore) {
      _state.updateLoadingStates(topShelf: true);

      if (isMore) {
        _state.topShelfLimit += 10;
      }
    }

    try {
      final responseMap = await _bookCaseController!.allBooksRead(
        _state.loggedInUserId!,
        _state.topShelfLimit,
        _state.offset,
        false,
        false,
        context!,
      );

      if (responseMap["statusCode"] == 200) {
        List<BookCaseModel> bookList = [];
        _state.updateCounts(topShelf: responseMap['count']);
        bookList = (responseMap["data"] as List)
            .map((item) => BookCaseModel.fromJson(item))
            .toList();

        var result = CommonHelper.getCurrentlyReadingAndTopShelfBooks(bookList);
        if (result.isNotEmpty) {
          _state.updateBookcaseData(
            topShelf: result[1],
            completed: result[2],
          );
          log("Completed Book Length: ${_state.completedBooks?.length}");
        }

        if ((_state.topShelfList?.length ?? 0) >= _state.topShelfCount) {
          _state.updateLoadingStates(topShelf: false);
        }
      }
    } catch (e) {
      log('ProfileHomeProvider: Error getting top shelf bookcase - $e');
    } finally {
      _state.updateLoadingStates(topShelf: false);
      notifyListeners();
    }
  }

  /// Get to-be-read books
  Future<void> getToBeReadBook(bool isMore, [BuildContext? context]) async {
    if (_bookCaseController == null || _state.loggedInUserId == null) return;

    if ((_state.toBeReadList?.length ?? 0) <= _state.toBeReadCount || !isMore) {
      _state.updateLoadingStates(toBeRead: true);

      if (isMore) {
        _state.toBeReadLimit += 10;
      }
    }

    log("Current Read Limit : ${_state.toBeReadLimit}");
    try {
      final responseMap = await _bookCaseController!.getToBeReadBook(
        _state.loggedInUserId!,
        _state.toBeReadLimit,
        _state.offset,
        context!,
      );

      log("Response Map : ${responseMap['statusCode']}");
      if (responseMap["statusCode"] == 200) {
        List<BookCaseModel> bookCaseList = [];
        if (responseMap["count"] != null) {
          _state.updateCounts(toBeRead: responseMap['count']);
        } else {
          _state.updateCounts(toBeRead: 0);
        }
        log("ToBeRead Count : ${_state.toBeReadCount}");

        if (responseMap["data"] != null) {
          bookCaseList = (responseMap["data"] as List)
              .map((item) => BookCaseModel.fromJson(item))
              .toList();
          _state.updateBookcaseData(toBeRead: bookCaseList);
        } else {
          _state.toBeReadList?.clear();
          _state.updateBookcaseData(toBeRead: []);
        }
        log("BookCase List : ${bookCaseList.length}");
      } else if (responseMap['statusCode'] == 404) {
        log("ToBeRead Read : ${_state.toBeReadList?.length}");
        _state.updateCounts(toBeRead: 0);
      }
    } catch (e) {
      log('ProfileHomeProvider: Error getting to-be-read books - $e');
    } finally {
      _state.updateLoadingStates(toBeRead: false);
      notifyListeners();
    }
    log("ToBeRead Read : ${_state.toBeReadList?.length}");
  }

  /// Get standing book clubs by user ID
  Future<void> getStandingBookClubsByUserId(bool isMore,
      [BuildContext? context]) async {
    if (_clubController == null || _state.loggedInUserId == null) return;

    await _clubController!
        .getBookClubsByUserId(
      context!,
      _state.loggedInUserId!,
      ClubType.standing,
      isMore,
    )
        .then((value) {
      _state.updateClubData(standing: _clubController!.standingBookClubList);
      notifyListeners();
    });
  }

  /// Get impromptu book clubs by user ID
  Future<void> getImpromptuBookClubsByUserId(bool isMore,
      [BuildContext? context]) async {
    if (_clubController == null || _state.loggedInUserId == null) return;

    await _clubController!
        .getBookClubsByUserId(
      context!,
      _state.loggedInUserId!,
      ClubType.impromptu,
      isMore,
    )
        .then((value) {
      _state.updateClubData(impromptu: _clubController!.impromptuBookClubList);
      notifyListeners();
    });
  }

  @override
  void dispose() {
    _syncSubscription?.cancel();
    _refreshSubscription?.cancel();
    _state.dispose();
    super.dispose();
  }
}
