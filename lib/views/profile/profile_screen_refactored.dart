import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../constants/constants.dart';
import '../../constants/text_style.dart';
import '../../controller/book_club_controller.dart';
import '../../models/book_club_model.dart';
import '../../reusableWidgets/connection_error/network_aware_tap.dart';
import '../../reusableWidgets/no_data_widget.dart';
import 'providers/profile_home_provider.dart';
import 'services/profile_sync_service.dart';
import 'widgets/skeletons/book_skeleton.dart';
import 'widgets/skeletons/club_skeleton.dart';

/// Refactored ProfileScreen using Provider pattern
/// Maintains 100% existing functionality while improving architecture
class ProfileScreenRefactored extends StatefulWidget {
  const ProfileScreenRefactored({super.key});

  @override
  State<ProfileScreenRefactored> createState() =>
      _ProfileScreenRefactoredState();
}

class _ProfileScreenRefactoredState extends State<ProfileScreenRefactored> {
  BookClubController? clubController;

  @override
  void initState() {
    super.initState();
    _initializeData();
    _setupProfileSyncListeners();
  }

  void _initializeData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<ProfileHomeProvider>(context, listen: false);
      clubController = Provider.of<BookClubController>(context, listen: false);

      // Initialize scroll listeners
      _setupScrollListeners(provider);

      // Load initial data
      provider.refreshAllData(context);
    });
  }

  void _setupScrollListeners(ProfileHomeProvider provider) {
    provider.state.currentReadScrollController
        .addListener(() => _currentReadOnScroll());
    provider.state.topShelfScrollController
        .addListener(() => _topShelfOnScroll());
    provider.state.standingClubScrollController
        .addListener(() => _standingOnScroll());
    provider.state.impromptuClubScrollController
        .addListener(() => _impromptuOnScroll());
    provider.state.toBeReadScrollController
        .addListener(() => _toBeReadOnScroll());
  }

  void _setupProfileSyncListeners() {
    ProfileSyncService().refreshStream.listen((_) {
      if (mounted) {
        final provider =
            Provider.of<ProfileHomeProvider>(context, listen: false);
        provider.refreshAllData(context);
      }
    });
  }

  void disposeProfileSyncListeners() {
    // Handled by ProfileSyncService dispose
  }

  // SCROLL HANDLERS
  void _currentReadOnScroll() {
    final provider = Provider.of<ProfileHomeProvider>(context, listen: false);
    final controller = provider.state.currentReadScrollController;

    if (controller.position.pixels >= controller.position.maxScrollExtent &&
        !provider.currentReadLoading &&
        (provider.currentBookCaseList?.length ?? 0) <=
            provider.currentReadCount) {
      CommonHelper.networkClose(
          provider.getCurrentReadBookCase(true, context), context);
    }
  }

  void _topShelfOnScroll() {
    final provider = Provider.of<ProfileHomeProvider>(context, listen: false);
    final controller = provider.state.topShelfScrollController;

    if (controller.position.pixels >= controller.position.maxScrollExtent &&
        !provider.topShelfLoading &&
        (provider.topShelfList?.length ?? 0) < provider.topShelfCount) {
      CommonHelper.networkClose(
          provider.getTopShelfBookCase(true, context), context);
    }
  }

  void _standingOnScroll() {
    final provider = Provider.of<ProfileHomeProvider>(context, listen: false);
    final controller = provider.state.standingClubScrollController;

    if (controller.position.pixels >= controller.position.maxScrollExtent &&
        !provider.standingClubLoading &&
        (provider.standingBookClubList?.length ?? 0) <
            provider.standingClubCount) {
      CommonHelper.networkClose(
          provider.getStandingBookClubsByUserId(true, context), context);
    }
  }

  void _impromptuOnScroll() {
    final provider = Provider.of<ProfileHomeProvider>(context, listen: false);
    final controller = provider.state.impromptuClubScrollController;

    if (controller.position.pixels >= controller.position.maxScrollExtent &&
        !provider
            .standingClubLoading && // Use standing club loading as fallback
        (provider.impromptuBookClubList?.length ?? 0) <
            provider.standingClubCount) {
      CommonHelper.networkClose(
          provider.getImpromptuBookClubsByUserId(true, context), context);
    }
  }

  void _toBeReadOnScroll() {
    final provider = Provider.of<ProfileHomeProvider>(context, listen: false);
    final controller = provider.state.toBeReadScrollController;

    if (controller.position.pixels >= controller.position.maxScrollExtent &&
        !provider.toBeReadLoading &&
        (provider.toBeReadList?.length ?? 0) < provider.toBeReadCount) {
      CommonHelper.networkClose(
          provider.getToBeReadBook(true, context), context);
    }
  }

  @override
  void dispose() {
    disposeProfileSyncListeners();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            filterQuality: FilterQuality.high,
            fit: BoxFit.cover,
          ),
        ),
        child: Consumer<ProfileHomeProvider>(
          builder: (context, provider, child) {
            final joiningDate = CommonHelper.getMonthYearDateFormat(
                provider.userModel.data?.userCreatedDate);

            return Skeletonizer(
              effect: const SoldColorEffect(
                color: AppConstants.skeletonforgroundColor,
                lowerBound: 0.1,
                upperBound: 0.5,
              ),
              containersColor: AppConstants.skeletonBackgroundColor,
              enabled: provider.isLoading,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile header section
                    _buildProfileHeader(provider, joiningDate),

                    const SizedBox(height: 25),
                    const Divider(
                        thickness: 1.5, color: AppConstants.primaryColor),
                    const SizedBox(height: 18),

                    // Bookcase section
                    _buildBookcaseSection(provider),

                    const SizedBox(height: 25),
                    const Divider(
                        thickness: 1.5, color: AppConstants.primaryColor),
                    const SizedBox(height: 18),

                    // Book clubs section
                    _buildBookClubsSection(provider),

                    const SizedBox(height: 25),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileHeader(ProfileHomeProvider provider, String joiningDate) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User location
          Row(
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width / 2,
                ),
                child: Text(
                  provider.userModel.data?.userLocation ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: lbRegular.copyWith(fontSize: 14),
                ),
              ),
              const Spacer(),
              Text(
                "Joined: $joiningDate",
                overflow: TextOverflow.ellipsis,
                style: lbRegular.copyWith(fontSize: 14),
              ),
            ],
          ),

          const SizedBox(height: 15),

          // User bio
          Text(
            provider.userModel.data?.userBio ?? '',
            style: lbRegular.copyWith(fontSize: 14),
          ),

          const SizedBox(height: 15),

          // Edit profile button
          NetworkAwareTap(
            onTap: () => _navigateToEditProfile(provider),
            child: Container(
              height: 45,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                color: AppConstants.textGreenColor,
                borderRadius: BorderRadius.circular(90),
              ),
              child: Center(
                child: Text(
                  "Edit Profile",
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(fontSize: 18),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBookcaseSection(ProfileHomeProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Bookcase header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            children: [
              Text("Bookcase", style: lbRegular.copyWith(fontSize: 24)),
              const SizedBox(width: 10),
              Image.asset("assets/icons/Bookcase_2.png", height: 25, width: 25),
            ],
          ),
        ),

        const SizedBox(height: 18),

        // Currently Reading section
        _buildCurrentlyReadingSection(provider),

        const SizedBox(height: 25),

        // To-Be-Read section
        _buildToBeReadSection(provider),

        const SizedBox(height: 25),

        // All Books Read section
        _buildAllBooksReadSection(provider),
      ],
    );
  }

  Widget _buildBookClubsSection(ProfileHomeProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Standing clubs
        _buildStandingClubsSection(provider),

        const SizedBox(height: 25),

        // Impromptu clubs
        _buildImpromptuClubsSection(provider),
      ],
    );
  }

  Widget _buildCurrentlyReadingSection(ProfileHomeProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            children: [
              Text("Currently Reading",
                  style: lbRegular.copyWith(fontSize: 20)),
              const SizedBox(width: 10),
              Image.asset(AppConstants.currentlyReadingIcon,
                  height: 20, width: 22),
            ],
          ),
        ),
        const SizedBox(height: 10),

        provider.currentBookCaseList?.isNotEmpty ?? false
            ? SizedBox(
                height: 80,
                child: ListView.builder(
                  controller: provider.state.currentReadScrollController,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(left: 10, right: 20),
                  itemCount: provider.currentReadLoading
                      ? (provider.currentBookCaseList?.length ?? 0) + 1
                      : provider.currentBookCaseList?.length,
                  itemBuilder: (context, index) {
                    if (index == provider.currentBookCaseList?.length &&
                        provider.currentReadLoading) {
                      return const Padding(
                        padding: EdgeInsets.only(left: 10.0),
                        child: Center(
                          child: CircularProgressIndicator(
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      );
                    }
                    return Skeleton.replace(
                      replacement: CurrentReadingSkeleton(
                        isBorder: true,
                        index: index,
                        bookList: provider.currentBookCaseList,
                      ),
                      child: CurrentReadingSkeleton(
                        isBorder: false,
                        index: index,
                        bookList: provider.currentBookCaseList,
                      ),
                    );
                  },
                ),
              )
            : const NoDataWidget(message: "Add books to currently reading"),

        const SizedBox(height: 25),

        // Edit Currently Reading button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: NetworkAwareTap(
            onTap: () => _navigateToEditCurrentReading(provider),
            child: Container(
              height: 45,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                color: AppConstants.textGreenColor,
                borderRadius: BorderRadius.circular(90),
              ),
              child: Center(
                child: Text(
                  "Edit Currently Reading",
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(fontSize: 18),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildToBeReadSection(ProfileHomeProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            children: [
              Text("To-Be-Read", style: lbRegular.copyWith(fontSize: 20)),
              const SizedBox(width: 10),
              Image.asset(AppConstants.toBeReadIcon, height: 20, width: 26),
            ],
          ),
        ),
        const SizedBox(height: 10),

        provider.toBeReadList?.isNotEmpty ?? false
            ? SizedBox(
                height: 80,
                child: ListView.builder(
                  controller: provider.state.toBeReadScrollController,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(left: 10, right: 20),
                  itemCount: provider.toBeReadLoading
                      ? (provider.toBeReadList?.length ?? 0) + 1
                      : provider.toBeReadList?.length,
                  itemBuilder: (context, index) {
                    if (index == provider.toBeReadList?.length &&
                        provider.toBeReadLoading) {
                      return const Padding(
                        padding: EdgeInsets.only(left: 10.0),
                        child: Center(
                          child: CircularProgressIndicator(
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      );
                    }
                    return Skeleton.replace(
                      replacement: ToBeReadSkeleton(
                        isBorder: true,
                        index: index,
                        bookList: provider.toBeReadList,
                      ),
                      child: ToBeReadSkeleton(
                        isBorder: false,
                        index: index,
                        bookList: provider.toBeReadList,
                      ),
                    );
                  },
                ),
              )
            : const NoDataWidget(message: "No books in To-Be-Read"),

        const SizedBox(height: 25),

        // Edit To-Be-Read button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: NetworkAwareTap(
            onTap: () => context.pushNamed('To-Be-Read'),
            child: Container(
              height: 45,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(49),
                color: AppConstants.textGreenColor,
              ),
              child: Center(
                child: Text(
                  "Edit To-Be-Read",
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(fontSize: 18),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAllBooksReadSection(ProfileHomeProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Row(
            children: [
              Text("All Books Read", style: lbRegular.copyWith(fontSize: 20)),
              const SizedBox(width: 10),
              Image.asset(AppConstants.bookReadIcon, height: 20, width: 22),
            ],
          ),
        ),
        const SizedBox(height: 10),

        provider.completedBooks?.isNotEmpty ?? false
            ? SizedBox(
                height: 117,
                child: ListView.builder(
                  controller: provider.state.topShelfScrollController,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(left: 10, right: 20),
                  itemCount: provider.topShelfLoading
                      ? (provider.completedBooks?.length ?? 0) + 1
                      : provider.completedBooks?.length,
                  itemBuilder: (context, index) {
                    if (index == provider.completedBooks?.length &&
                        provider.topShelfLoading) {
                      return const Padding(
                        padding: EdgeInsets.only(left: 10.0),
                        child: Center(
                          child: CircularProgressIndicator(
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      );
                    }
                    return Skeleton.replace(
                      replacement: CompletedBookSkeleton(
                        isBorder: true,
                        index: index,
                        bookList: provider.completedBooks,
                        userName: provider.userModel.data?.userName,
                        userHandler: provider.userModel.data?.userHandle,
                        userModel: provider.userModel,
                      ),
                      child: CompletedBookSkeleton(
                        isBorder: false,
                        index: index,
                        bookList: provider.completedBooks,
                        userName: provider.userModel.data?.userName,
                        userHandler: provider.userModel.data?.userHandle,
                        userModel: provider.userModel,
                      ),
                    );
                  },
                ),
              )
            : const NoDataWidget(message: "No books read"),

        const SizedBox(height: 25),

        // View/Edit All Books Read button
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: NetworkAwareTap(
            onTap: () => _navigateToEditBookcase(provider),
            child: Container(
              height: 45,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(49),
                color: AppConstants.textGreenColor,
              ),
              child: Center(
                child: Text(
                  "View/Edit All Books Read",
                  textAlign: TextAlign.center,
                  style: lbBold.copyWith(fontSize: 18),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStandingClubsSection(ProfileHomeProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text("Standing Book Clubs",
              style: lbRegular.copyWith(fontSize: 20)),
        ),
        const SizedBox(height: 10),
        provider.standingBookClubList?.isNotEmpty ?? false
            ? SizedBox(
                height: 132,
                child: ListView.builder(
                  controller: provider.state.standingClubScrollController,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(left: 10, right: 20),
                  itemCount: provider.standingClubLoading
                      ? (provider.standingBookClubList?.length ?? 0) + 1
                      : provider.standingBookClubList?.length,
                  itemBuilder: (context, index) {
                    if (index == provider.standingBookClubList?.length &&
                        provider.standingClubLoading) {
                      return const Padding(
                        padding: EdgeInsets.only(left: 10.0),
                        child: Center(
                          child: CircularProgressIndicator(
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      );
                    }
                    return NetworkAwareTap(
                      onTap: () {
                        Provider.of<BookClubController>(context, listen: false)
                            .updateData(provider.standingBookClubList?[index] ??
                                BookClubModel());
                        navigateToBookClubDetails(
                            provider.standingBookClubList?[index] ??
                                BookClubModel());
                      },
                      child: Skeleton.replace(
                        replacement: StandingClubSkeleton(
                          isBorder: true,
                          index: index,
                          clubList: provider.standingBookClubList,
                          loggedInUserId: provider.userModel.data?.userId,
                        ),
                        child: StandingClubSkeleton(
                          isBorder: false,
                          index: index,
                          clubList: provider.standingBookClubList,
                          loggedInUserId: provider.userModel.data?.userId,
                        ),
                      ),
                    );
                  },
                ),
              )
            : const NoDataWidget(message: "Join or create a standing club"),
      ],
    );
  }

  Widget _buildImpromptuClubsSection(ProfileHomeProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          child: Text("Impromptu Book Clubs",
              style: lbRegular.copyWith(fontSize: 20)),
        ),
        const SizedBox(height: 10),
        provider.impromptuBookClubList?.isNotEmpty ?? false
            ? SizedBox(
                height: 181,
                child: ListView.builder(
                  controller: provider.state.impromptuClubScrollController,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(left: 10, right: 20),
                  itemCount: provider.standingClubLoading
                      ? (provider.impromptuBookClubList?.length ?? 0) + 1
                      : provider.impromptuBookClubList?.length,
                  itemBuilder: (context, index) {
                    if (index == provider.impromptuBookClubList?.length &&
                        provider.standingClubLoading) {
                      return const Padding(
                        padding: EdgeInsets.only(left: 10.0),
                        child: Center(
                          child: CircularProgressIndicator(
                            color: AppConstants.primaryColor,
                          ),
                        ),
                      );
                    }
                    return NetworkAwareTap(
                      onTap: () {
                        Provider.of<BookClubController>(context, listen: false)
                            .updateData(
                                provider.impromptuBookClubList?[index] ??
                                    BookClubModel());
                        navigateToBookClubDetails(
                            provider.impromptuBookClubList?[index] ??
                                BookClubModel());
                      },
                      child: Skeleton.replace(
                        replacement: ImpromptuClubSkeleton(
                          isBorder: true,
                          index: index,
                          clubList: provider.impromptuBookClubList,
                          loggedInUserId: provider.userModel.data?.userId,
                        ),
                        child: ImpromptuClubSkeleton(
                          isBorder: false,
                          index: index,
                          clubList: provider.impromptuBookClubList,
                          loggedInUserId: provider.userModel.data?.userId,
                        ),
                      ),
                    );
                  },
                ),
              )
            : const NoDataWidget(message: 'Join or create an impromptu club'),
      ],
    );
  }

  // Navigation methods
  void _navigateToEditProfile(ProfileHomeProvider provider) {
    context.pushNamed(
      'EditProfileScreen',
      extra: {
        'buttonName': 'Edit Profile',
        "userModel": provider.userModel,
        'isCompleteProfile': true,
      },
    );
  }

  void _navigateToEditCurrentReading(ProfileHomeProvider provider) {
    context.pushNamed(
      'EditCurrentReadBook',
      extra: {
        'buttonName': "Edit Currently Reading",
        'topShelfList': provider.topShelfList,
        'completedBookList': provider.completedBooks,
        'userId': provider.userModel.data?.userId,
      },
    );
  }

  void _navigateToEditBookcase(ProfileHomeProvider provider) {
    context.pushNamed(
      'EditBookCase',
      extra: {
        'userProfilePicture': provider.userModel.data?.userProfilePicture,
        'handler': provider.userModel.data?.userHandle,
        'userName': provider.userModel.data?.userName,
        'bookCaseList': provider.bookCase,
        'topShelfLength': provider.topShelfList?.length,
        'userClubInvitation': provider.userModel.data?.userClubInvitation,
      },
    );
  }

  void navigateToBookClubDetails(BookClubModel bookClubItem) {
    final provider = Provider.of<ProfileHomeProvider>(context, listen: false);
    context.pushNamed(
      'user-club-details',
      queryParameters: {
        'bookClubId': bookClubItem.bookClubId.toString(),
        'userId': provider.userModel.data?.userId.toString() ?? '',
      },
    );
  }
}
