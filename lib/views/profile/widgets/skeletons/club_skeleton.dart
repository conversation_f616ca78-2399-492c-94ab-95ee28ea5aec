import 'package:flutter/material.dart';
import '../../../../constants/constants.dart';
import '../../../../constants/text_style.dart';
import '../../../../models/book_club_model.dart';
import '../../../../reusableWidgets/marquee_text.dart';

/// Generic club skeleton widget for both standing and impromptu clubs
class ClubSkeleton extends StatelessWidget {
  final bool isBorder;
  final int index;
  final List<BookClubModel>? clubList;
  final double? height;
  final double? width;
  final bool isImpromptu;
  final int? loggedInUserId;
  final VoidCallback? onTap;

  const ClubSkeleton({
    super.key,
    required this.isBorder,
    required this.index,
    this.clubList,
    this.height,
    this.width = 228,
    this.isImpromptu = false,
    this.loggedInUserId,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(left: 10),
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: isBorder
              ? AppConstants.skeletonBackgroundColor
              : Colors.transparent,
          border: Border.all(
            color: AppConstants.primaryColor,
            width: 1.5,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(14.0),
          child: Column(
            children: [
              // Club icon with leader star overlay
              _buildClubIcon(),
              const SizedBox(height: 25),
              
              // Club name
              Flexible(
                child: MarqueeList(
                  children: [
                    Text(
                      clubList?[index].bookClubName ?? '',
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                      style: lbBold.copyWith(
                        fontSize: 18,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Additional content for impromptu clubs
              if (isImpromptu) ...[
                const SizedBox(height: 3),
                Text(
                  clubList?[index].bookAuthor ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: lbRegular.copyWith(
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                Text(
                  clubList?[index].clubCount ?? '',
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  style: lbItalic.copyWith(
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildClubIcon() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Leader star (left side)
        Align(
          alignment: Alignment.centerLeft,
          child: Visibility(
            visible: clubList?[index].userId == loggedInUserId,
            replacement: const SizedBox.shrink(),
            child: Image.asset(
              AppConstants.leaderStar,
              height: 43,
              width: 43,
              fit: BoxFit.cover,
              filterQuality: FilterQuality.high,
            ),
          ),
        ),
        
        // Club status icon (center)
        Align(
          alignment: Alignment.center,
          child: Image.asset(
            (clubList?[index].totalVacancies ?? 0) > 0
                ? AppConstants.clubOpeningLogoImagePath
                : AppConstants.clubOpeningZero,
            height: 50,
            width: 50,
            fit: BoxFit.cover,
            filterQuality: FilterQuality.high,
          ),
        ),
      ],
    );
  }
}

/// Standing club skeleton
class StandingClubSkeleton extends StatelessWidget {
  final bool isBorder;
  final int index;
  final List<BookClubModel>? clubList;
  final int? loggedInUserId;
  final VoidCallback? onTap;

  const StandingClubSkeleton({
    super.key,
    required this.isBorder,
    required this.index,
    this.clubList,
    this.loggedInUserId,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ClubSkeleton(
      isBorder: isBorder,
      index: index,
      clubList: clubList,
      height: 132,
      width: 228,
      isImpromptu: false,
      loggedInUserId: loggedInUserId,
      onTap: onTap,
    );
  }
}

/// Impromptu club skeleton
class ImpromptuClubSkeleton extends StatelessWidget {
  final bool isBorder;
  final int index;
  final List<BookClubModel>? clubList;
  final int? loggedInUserId;
  final VoidCallback? onTap;

  const ImpromptuClubSkeleton({
    super.key,
    required this.isBorder,
    required this.index,
    this.clubList,
    this.loggedInUserId,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ClubSkeleton(
      isBorder: isBorder,
      index: index,
      clubList: clubList,
      height: 181,
      width: 228,
      isImpromptu: true,
      loggedInUserId: loggedInUserId,
      onTap: onTap,
    );
  }
}
