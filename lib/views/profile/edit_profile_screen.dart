import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:eljunto/constants/config.dart';
import 'package:eljunto/constants/constants.dart';
import 'package:eljunto/constants/text_style.dart';
import 'package:eljunto/controller/profile_controller.dart';
import 'package:eljunto/controller/user_controller.dart';
import 'package:eljunto/models/profile_model/edit_profile/update_user_profile_model.dart';
import 'package:eljunto/models/profile_model/location_model.dart';
import 'package:eljunto/reusableWidgets/connection_error/network_aware_tap.dart';
import 'package:eljunto/reusableWidgets/imageBuilder.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../constants/common_helper.dart';
import '../../models/user_model.dart';
import '../../reusableWidgets/custom_button.dart';
import '../../reusableWidgets/previous_screen_appbar.dart';
import 'providers/profile_home_provider.dart';
import 'services/profile_sync_service.dart';

/// Refactored EditProfileScreen using Provider pattern
/// Maintains 100% existing functionality while improving architecture
class EditProfileScreen extends StatefulWidget {
  final String? buttonName;
  final bool updateProfile;
  final UserModel? userModel;

  const EditProfileScreen({
    super.key,
    this.buttonName,
    this.updateProfile = false,
    this.userModel,
  });

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  // Form controllers
  final TextEditingController handleController = TextEditingController();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController locationController = TextEditingController();
  final TextEditingController bioController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Form state
  String? clubInvitation = "";
  bool isNotification = true;
  bool nameValidation = false;
  bool locationValidation = false;
  bool bioValidation = false;
  String errorMsg = '';
  String imageValidation = '';

  // Image handling
  File? _image;
  String userProfilePicture = '';
  bool isImageLoading = false;

  // Loading states
  bool isLoading = false;
  bool isSaving = false;
  bool isDataLoading = false;

  // User data
  int? userId;
  String? logginedMail;

  // Controllers
  UserController? userController;
  ProfileController? profileController;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeData();
  }

  void _initializeControllers() {
    userController = Provider.of<UserController>(context, listen: false);
    profileController = Provider.of<ProfileController>(context, listen: false);
  }

  void _initializeData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final provider = Provider.of<ProfileHomeProvider>(context, listen: false);

      // Initialize provider if not already done
      if (provider.loggedInUserId == null) {
        provider.initialize(context);
      }

      await _initializeUserId();
      await _loadUserData(provider);
    });
  }

  /// Load user data using Provider pattern
  Future<void> _loadUserData(ProfileHomeProvider provider) async {
    setState(() => isDataLoading = true);

    try {
      // Use existing provider data if available, otherwise fetch fresh data
      if (provider.userModel.data?.userId != null) {
        await _initializeFormFields(provider.userModel);
      } else {
        // Fallback to direct API call if provider data not available
        await _fetchUserDetailsDirectly();
      }
    } catch (e) {
      log('Error loading user data: $e');
      // Fallback to direct API call on error
      await _fetchUserDetailsDirectly();
    }

    setState(() => isDataLoading = false);
  }

  /// Fetch user details directly from API (fallback method)
  Future<void> _fetchUserDetailsDirectly() async {
    try {
      await userController?.getUserDetailsByUserId(userId ?? 0, context);
      final userModel = userController?.userModel ?? UserModel();
      await _initializeFormFields(userModel);
    } catch (e) {
      log('Error fetching user details: $e');
    }
  }

  /// Initialize form fields with user data
  Future<void> _initializeFormFields(UserModel userModel) async {
    await _populateFormFields(userModel);
    _setClubInvitationStatus(userModel);
  }

  /// Populate form fields with user data
  Future<void> _populateFormFields(UserModel userModel) async {
    handleController.text = userModel.data?.userHandle ?? '';
    locationController.text = userModel.data?.userLocation ?? '';
    bioController.text = userModel.data?.userBio ?? '';
    nameController.text = userModel.data?.userName ?? '';
    userProfilePicture = userModel.data?.userProfilePicture?.isNotEmpty ?? false
        ? Config.imageBaseUrl + (userModel.data?.userProfilePicture ?? '')
        : AppConstants.profileLogoImagePath;
  }

  /// Set club invitation status from user data
  void _setClubInvitationStatus(UserModel userModel) {
    if (userModel.data?.userClubInvitation == true) {
      clubInvitation = "Yes";
      isNotification = true;
    } else {
      clubInvitation = "No";
      isNotification = false;
    }
    setState(() {});
  }

  // Location data (kept for potential future use)
  List<Location>? locationList;
  int? locationCountryId;

  Future<void> _initializeUserId() async {
    userId = await CommonHelper.getLoggedInUserId();

    String? userMail = await CommonHelper.getLoggedinUserMail();
    String? userLocation = await CommonHelper.getLoggedinUserLocation();
    String? userBio = await CommonHelper.getLoggedinUserBio();

    if (userLocation != null && userBio != null) {}
    if (userId != null && userMail != null) {
      setState(() {
        logginedMail = userMail;
      });
    } else {}
  }

  Future<void> isLocationUpdate() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    nameController.text = pref.getString('').toString();
  }

  Future<void> _pickImage() async {
    setState(() {
      isImageLoading = true;
    });

    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(
      source: ImageSource.gallery,
      imageQuality: 100,
      maxHeight: 1000,
      maxWidth: 1000,
    );

    if (pickedFile != null) {
      final fileSize = await File(pickedFile.path).length();
      const maxSizeInBytes = 10 * 1024 * 1024;

      if (fileSize > maxSizeInBytes) {
        setState(() {
          isImageLoading = false;
          imageValidation =
              'Selected image exceeds the maximum allowed size of 10 MB.';
        });
        return;
      }

      // Validate file type (MIME type)
      final mimeType = CommonHelper.getMimeType(
          pickedFile.path); //pickedFile.mimeType?.toLowerCase();
      const allowedMimeTypes = ['image/jpeg', 'image/png'];

      if (mimeType == null || !allowedMimeTypes.contains(mimeType)) {
        setState(() {
          isImageLoading = false;
          imageValidation =
              'Selected file is not a valid image type (JPEG/PNG).';
        });
        return;
      }
      final file = await cropUserImage(pickedFile);

      final croppedImage = await convertSquareImageToCircle(File(file!.path));
      if (croppedImage.path.isNotEmpty) {
        final renamedCroppedImage =
            await CommonHelper.renameFile(File(pickedFile.path), croppedImage);
        setState(() {
          _image = renamedCroppedImage;
        });

        try {
          if (mounted) {
            final responseMap =
                await Provider.of<UserController>(context, listen: false)
                    .uploadFile(_image!, userId ?? 0, context);
            if (responseMap.containsKey('error')) {
              log(responseMap['error']);
            } else {
              setState(() {
                userProfilePicture = Config.imageBaseUrl + responseMap["data"];
              });
            }
          }
        } catch (e) {
          log('Error uploading image: $e');
          // Handle error
        }
      }
    } else {
      log('Image not picked');
      setState(() {
        _image = null;
      });
    }

    setState(() {
      isImageLoading = false; // Stop loading indicator
    });
  }

  Future<File> convertSquareImageToCircle(File imageFile) async {
    final image = await decodeImageFromList(await imageFile.readAsBytes());
    final output = await _createCircleImage(image);
    final file = File(imageFile.path);
    await file.writeAsBytes(output);
    return file;
  }

  Future<Uint8List> _createCircleImage(ui.Image image) async {
    final pictureRecorder = ui.PictureRecorder();
    final canvas = Canvas(pictureRecorder);

    final size = image.width;
    final radius = size / 2;

    // Draw the circle
    final paint = Paint();

    paint.color = Colors.transparent;

    canvas.drawCircle(Offset(radius, radius), radius, paint);

    // Clip the image to the circle

    canvas.clipPath(
      Path()
        ..addOval(
          Rect.fromCircle(
            center: Offset(radius, radius),
            radius: radius,
          ),
        ),
    );

    // Draw the image centered in the circle

    canvas.drawImage(image, const Offset(0, 0), Paint());

    final picture = pictureRecorder.endRecording();
    final img = await picture.toImage(size, size);
    final byteData = await img.toByteData(format: ui.ImageByteFormat.png);
    return byteData!.buffer.asUint8List();
  }

  Future<CroppedFile?> cropUserImage(XFile pickedFile) async {
    final croppedImage = await ImageCropper().cropImage(
      sourcePath: pickedFile.path,
      aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1),
      compressFormat: ImageCompressFormat.png,
      compressQuality: 100,
      uiSettings: [
        AndroidUiSettings(
          toolbarTitle: 'Crop your image',
          toolbarColor: AppConstants.primaryColor,
          cropFrameColor: AppConstants.primaryColor,
          toolbarWidgetColor: Colors.white,
          cropStyle: CropStyle.circle,
          hideBottomControls: true,
          // initAspectRatio: CropAspectRatioPreset.original,
          lockAspectRatio: true,
          showCropGrid: true,
        ),
        IOSUiSettings(
          minimumAspectRatio: 1,
          title: 'Crop your image',
          doneButtonTitle: 'Save',
          cancelButtonTitle: 'Cancel',
          cropStyle: CropStyle.circle,
          showCancelConfirmationDialog: true,
          hidesNavigationBar: false,
          rotateButtonsHidden: true,
          rotateClockwiseButtonHidden: true,
          aspectRatioPickerButtonHidden: true,
          aspectRatioLockEnabled: true,
        ),
      ],
    );
    return croppedImage;
  }

  @override
  void dispose() {
    handleController.dispose();
    nameController.dispose();
    bioController.dispose();
    locationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Container(
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.width,
        decoration: const BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AppConstants.bgImagePath),
            filterQuality: FilterQuality.high,
            fit: BoxFit.fill,
          ),
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(80),
            child: Container(
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    width: 1.5,
                    color: AppConstants.primaryColor,
                  ),
                ),
              ),
              child: PreviousScreenAppBar(
                bookName: widget.buttonName,
                isSetProfile: widget.updateProfile,
              ),
            ),
          ),
          body: Consumer<ProfileHomeProvider>(
            builder: (context, provider, child) {
              return Skeletonizer(
                enabled: isDataLoading,
                child: Form(
                  key: _formKey,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Column(
                      children: [
                        Expanded(
                          child: SingleChildScrollView(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const SizedBox(
                                  height: 25,
                                ),
                                ListTile(
                                  contentPadding: const EdgeInsets.symmetric(
                                    vertical: 1.5,
                                    horizontal: 16,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10),
                                    side: const BorderSide(
                                      color: AppConstants.primaryColor,
                                      width: 1.5,
                                    ),
                                  ),
                                  dense: true,
                                  tileColor: Colors.transparent,
                                  title: Text(
                                    'Edit Profile Photo',
                                    // textAlign: ui.TextAlign.center,
                                    overflow: TextOverflow.ellipsis,
                                    style: lbRegular.copyWith(
                                      fontSize: 18,
                                    ),
                                  ),
                                  leading: ClipRRect(
                                    clipBehavior: ui.Clip.none,
                                    borderRadius: BorderRadius.circular(50),
                                    child: CustomCachedNetworkImage(
                                      errorImage:
                                          AppConstants.profileLogoImagePath,
                                      height: 45,
                                      width: 45,
                                      imageUrl: userProfilePicture,
                                    ),
                                  ),
                                  onTap: () {
                                    _pickImage();
                                  },
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 20.0),
                                  child: Row(
                                    children: [
                                      (imageValidation.isNotEmpty)
                                          ? Text(
                                              imageValidation,
                                              overflow: TextOverflow.clip,
                                              style: lbBold.copyWith(
                                                fontSize: 14,
                                                color: AppConstants.redColor,
                                              ),
                                            )
                                          : const SizedBox.shrink(),
                                    ],
                                  ),
                                ),
                                const SizedBox(
                                  height: 25,
                                ),
                                Text(
                                  'Handle:',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                TextFormField(
                                  controller: handleController,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                  // maxLines: null,
                                  enabled: false,
                                  decoration: InputDecoration(
                                    filled: true,
                                    fillColor:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    contentPadding: const EdgeInsets.all(10),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(10),
                                      borderSide: const BorderSide(
                                        color: AppConstants.primaryColor,
                                        width: 1.5,
                                      ),
                                    ),
                                  ),
                                  readOnly: true,
                                ),
                                const SizedBox(
                                  height: 25,
                                ),
                                Text(
                                  'Name:',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Stack(
                                  clipBehavior: ui.Clip.none,
                                  children: [
                                    TextFormField(
                                      controller: nameController,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                      maxLines: null,
                                      // maxLength: 45,
                                      decoration: InputDecoration(
                                        // counterStyle: lbRegular.copyWith(
                                        //   fontSize: 14,
                                        // ),
                                        suffixIcon: GestureDetector(
                                          onTap: () {},
                                          child: Image.asset(
                                            'assets/icons/Edit.png',
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                            height: 38,
                                            width: 38,
                                          ),
                                        ),
                                        filled: true,
                                        fillColor: const Color.fromRGBO(
                                            255, 255, 255, 1),
                                        contentPadding:
                                            const EdgeInsets.all(10),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          borderSide: const BorderSide(
                                            color: AppConstants.primaryColor,
                                            width: 1.5,
                                          ),
                                        ),
                                      ),
                                      onChanged: (value) {
                                        setState(() {
                                          nameValidation = false;
                                        });
                                      },
                                      validator: (value) {
                                        nameValidation = false;
                                        if (value!.isEmpty) {
                                          setState(() {
                                            nameValidation = true;
                                          });
                                          return null;
                                        } else {
                                          return null;
                                        }
                                      },
                                    ),
                                    Positioned(
                                      left: 0,
                                      right: 0,
                                      top: 55,
                                      child: nameValidation
                                          ? Text(
                                              '*Enter name',
                                              overflow: TextOverflow.clip,
                                              style: lbBold.copyWith(
                                                fontSize: 14,
                                                color: AppConstants.redColor,
                                              ),
                                            )
                                          : const SizedBox.shrink(),
                                    ),
                                  ],
                                ),
                                nameValidation
                                    ? const SizedBox(
                                        height: 15,
                                      )
                                    : const SizedBox.shrink(),
                                const SizedBox(
                                  height: 25,
                                ),
                                Text(
                                  'Location:',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Stack(
                                  clipBehavior: ui.Clip.none,
                                  children: [
                                    TextFormField(
                                      controller: locationController,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                      maxLength: 30,
                                      maxLines: null,
                                      decoration: InputDecoration(
                                        counterStyle: lbRegular.copyWith(
                                          fontSize: 14,
                                        ),
                                        suffixIcon: NetworkAwareTap(
                                          onTap: () {},
                                          child: Image.asset(
                                            'assets/icons/Edit.png',
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                            height: 38,
                                            width: 38,
                                          ),
                                        ),
                                        filled: true,
                                        fillColor: const Color.fromRGBO(
                                            255, 255, 255, 1),
                                        contentPadding:
                                            const EdgeInsets.all(10),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          borderSide: const BorderSide(
                                            color: AppConstants.primaryColor,
                                            width: 1.5,
                                          ),
                                        ),
                                      ),
                                      onChanged: (value) {
                                        setState(() {
                                          locationValidation = false;
                                        });
                                      },
                                      validator: (value) {
                                        locationValidation = false;
                                        if (value!.isEmpty) {
                                          setState(() {
                                            locationValidation = true;
                                          });
                                          return null;
                                        } else {
                                          return null;
                                        }
                                      },
                                    ),
                                    // TypeAheadField(
                                    //   loadingBuilder: (context) {
                                    //     return Container(
                                    //       decoration: BoxDecoration(
                                    //         color: Colors.white,
                                    //         border: Border.all(
                                    //             color: AppConstants.primaryColor),
                                    //         borderRadius: BorderRadius.circular(10),
                                    //       ),
                                    //       child: const Center(
                                    //         child: CircularProgressIndicator(
                                    //           color: AppConstants.primaryColor,
                                    //         ),
                                    //       ),
                                    //     );
                                    //   },
                                    //   emptyBuilder: (context) {
                                    //     return ListTile(
                                    //       title: Text(
                                    //         'Location not found? Create your own now!',
                                    //         style: lbRegular.copyWith(
                                    //           fontSize: 14,
                                    //         ),
                                    //       ),
                                    //     );
                                    //   },
                                    //   controller: locationController,
                                    //   builder: (context, controller, focusNode) {
                                    //     return TextFormField(
                                    //       style: lbRegular.copyWith(
                                    //         fontSize: 18,
                                    //       ),
                                    //       controller: controller,
                                    //       focusNode: focusNode,
                                    //       maxLength: 30,
                                    //       decoration: InputDecoration(
                                    //         counterStyle: lbRegular.copyWith(
                                    //           fontSize: 14,
                                    //           color: AppConstants.primaryColor,
                                    //         ),
                                    //         suffixIcon: const Icon(
                                    //           Icons.search_rounded,
                                    //           size: 25,
                                    //           color: AppConstants.primaryColor,
                                    //         ),
                                    //         filled: true,
                                    //         fillColor: Colors.white,
                                    //         contentPadding: const EdgeInsets.all(10),
                                    //         border: OutlineInputBorder(
                                    //           borderRadius: BorderRadius.circular(5),
                                    //           borderSide: const BorderSide(
                                    //             color: AppConstants.primaryColor,
                                    //             width: 1.5,
                                    //           ),
                                    //         ),
                                    //       ),
                                    //       validator: (value) {
                                    //         locationValidation = false;
                                    //         if (value!.isEmpty) {
                                    //           setState(() {
                                    //             locationValidation = true;
                                    //           });
                                    //           return null;
                                    //         } else {
                                    //           return null;
                                    //         }
                                    //       },
                                    //       onChanged: (value) {
                                    //         setState(
                                    //           () {
                                    //             locationValidation = false;
                                    //           },
                                    //         );
                                    //       },
                                    //     );
                                    //   },
                                    //   decorationBuilder: (context, child) {
                                    //     return Material(
                                    //       type: MaterialType.card,
                                    //       elevation: 0,
                                    //       color: Colors.white,
                                    //       shape: RoundedRectangleBorder(
                                    //         borderRadius: BorderRadius.circular(10),
                                    //         side: const BorderSide(
                                    //           color: AppConstants.primaryColor,
                                    //         ),
                                    //       ),
                                    //       child: child,
                                    //     );
                                    //   },
                                    //   itemBuilder: (context, value) {
                                    //     return ListTile(
                                    //       title: Text(
                                    //         "${value.name ?? ''}, ${value.stateCode}",
                                    //         style: lbRegular.copyWith(fontSize: 15),
                                    //       ),
                                    //       shape: const Border(
                                    //         bottom: BorderSide(
                                    //           color: AppConstants.primaryColor,
                                    //         ),
                                    //       ),
                                    //     );
                                    //   },
                                    //   onSelected: (value) {
                                    //     locationController.text =
                                    //         "${value.name}, ${value.stateCode}";
                                    //     locationCountryId = value.countryId;
                                    //     // print("BookName : ${bookController.text}");
                                    //     // print("BookId : $bookId");
                                    //     setState(() {});
                                    //   },
                                    //   suggestionsCallback: (search) {
                                    //     if (locationController.length >= 3) {
                                    //       return _fetchLocationsData(search);
                                    //     }
                                    //     return null;
                                    //   },
                                    // ),
                                    Positioned(
                                      left: 0,
                                      right: 0,
                                      top: 55,
                                      child: locationController.text.isEmpty
                                          ? Text(
                                              '*Enter location',
                                              overflow: TextOverflow.clip,
                                              style: lbBold.copyWith(
                                                fontSize: 14,
                                                color: AppConstants.redColor,
                                              ),
                                            )
                                          : const SizedBox.shrink(),
                                    ),
                                  ],
                                ),
                                locationValidation
                                    ? const SizedBox(
                                        height: 15,
                                      )
                                    : const SizedBox.shrink(),
                                const SizedBox(
                                  height: 25,
                                ),
                                Text(
                                  'Bio (150 Characters max):',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Stack(
                                  clipBehavior: ui.Clip.none,
                                  children: [
                                    TextFormField(
                                      maxLength: 150,
                                      controller: bioController,
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                      maxLines: null,
                                      decoration: InputDecoration(
                                        counterStyle: lbRegular.copyWith(
                                          fontSize: 14,
                                          color: AppConstants.primaryColor,
                                        ),
                                        suffixIcon: NetworkAwareTap(
                                          onTap: () {},
                                          child: Image.asset(
                                            'assets/icons/Edit.png',
                                            filterQuality: FilterQuality.high,
                                            fit: BoxFit.cover,
                                            height: 38,
                                            width: 38,
                                          ),
                                        ),
                                        filled: true,
                                        fillColor: const Color.fromRGBO(
                                            255, 255, 255, 1),
                                        contentPadding:
                                            const EdgeInsets.all(10),
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          borderSide: const BorderSide(
                                            color: AppConstants.primaryColor,
                                            width: 1.5,
                                          ),
                                        ),
                                      ),
                                      onChanged: (value) {
                                        setState(() {
                                          bioValidation = false;
                                        });
                                      },
                                      validator: (value) {
                                        bioValidation = false;
                                        if (value!.isEmpty) {
                                          setState(() {
                                            bioValidation = true;
                                          });
                                          return null;
                                        } else {
                                          return null;
                                        }
                                      },
                                    ),
                                    Positioned(
                                      left: 0,
                                      right: 0,
                                      top: 55,
                                      child: bioController.text.isEmpty
                                          ? Text(
                                              '*Enter bio',
                                              overflow: TextOverflow.clip,
                                              style: lbBold.copyWith(
                                                fontSize: 14,
                                                color: AppConstants.redColor,
                                              ),
                                            )
                                          : const SizedBox.shrink(),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 25,
                                ),
                                Text(
                                  'Open to club invitations ?',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbRegular.copyWith(
                                    fontSize: 18,
                                  ),
                                ),
                                const SizedBox(
                                  height: 3,
                                ),
                                Text(
                                  '(Must be set to “Yes” to receive new club invitations)',
                                  overflow: TextOverflow.ellipsis,
                                  style: lbItalic.copyWith(
                                    fontSize: 14,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Row(
                                  children: [
                                    SizedBox(
                                      height: 19,
                                      width: 19,
                                      child: Radio<String>(
                                        fillColor: const WidgetStatePropertyAll(
                                          AppConstants.primaryColor,
                                        ),
                                        value: 'Yes',
                                        groupValue: clubInvitation,
                                        onChanged: (String? value) {
                                          setState(() {
                                            clubInvitation = value!;
                                            _filterData(clubInvitation);
                                          });
                                        },
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 15,
                                    ),
                                    Text(
                                      'Yes',
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 25,
                                    ),
                                    SizedBox(
                                      height: 19,
                                      width: 19,
                                      child: Radio<String>(
                                        fillColor: const WidgetStatePropertyAll(
                                          AppConstants.primaryColor,
                                        ),
                                        value: 'No',
                                        groupValue: clubInvitation,
                                        onChanged: (String? value) {
                                          setState(() {
                                            clubInvitation = value!;
                                            _filterData(clubInvitation);
                                          });
                                        },
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 15,
                                    ),
                                    Text(
                                      'No',
                                      style: lbRegular.copyWith(
                                        fontSize: 18,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 25,
                                ),
                                CustomLoaderButton(
                                  buttonWidth: isSaving
                                      ? 45.0
                                      : MediaQuery.of(context).size.width,
                                  buttonRadius: 30.0,
                                  buttonChild: isSaving
                                      ? const CircularProgressIndicator(
                                          valueColor: AlwaysStoppedAnimation(
                                              Colors.white),
                                          strokeWidth: 3.0,
                                        )
                                      : Text(
                                          'Save',
                                          style: lbBold.copyWith(
                                            fontSize: 18,
                                            color: AppConstants.primaryColor,
                                          ),
                                        ),
                                  buttonPressed: isSaving
                                      ? null
                                      : () {
                                          saveInfo();
                                        },
                                ),
                                const SizedBox(
                                  height: 25,
                                ),
                              ],
                            ),
                          ),
                        ),
                        // CustomLoaderButton(
                        //   // loginText: 'Login',
                        //   buttonWidth: isLoading
                        //       ? 45.0
                        //       : MediaQuery.of(context).size.width,
                        //   buttonRadius: 30.0,
                        //   buttonChild: isLoading
                        //       ? const CircularProgressIndicator(
                        //           valueColor:
                        //               AlwaysStoppedAnimation(Colors.white),
                        //           strokeWidth: 3.0,
                        //         )
                        //       : Text(
                        //           'Save',
                        //           style: lbBold.copyWith(
                        //             fontSize: 18,
                        //             color: AppConstants.primaryColor,
                        //           ),
                        //         ),
                        //   buttonPressed: () {
                        //     saveInfo();
                        //   },
                        // ),
                        // const SizedBox(
                        //   height: 25,
                        // ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void _filterData(String? selectedOutcome) {
    setState(() {
      if (selectedOutcome == 'Yes') {
        isNotification = true;
      } else {
        isNotification = false;
      }
    });
  }

  /// Save profile information with Provider pattern and cross-screen sync
  Future<bool> saveInfo() async {
    FocusScope.of(context).requestFocus(FocusNode());
    bool validation = _formKey.currentState!.validate();

    if (validation &&
        nameController.text.isNotEmpty &&
        locationController.text.isNotEmpty &&
        bioController.text.isNotEmpty) {
      setState(() => isSaving = true);

      try {
        // Prepare user data for update
        var userData = UserProfileUpdateModel(
          userId: userId,
          userEmailId: logginedMail,
          userProfilePicture: _image.toString(),
          userName: nameController.text,
          userLocation: locationController.text,
          userBio: bioController.text,
          userClubInvitation: isNotification,
        );

        // Update profile via API
        bool success =
            await profileController?.updateProfileFunction(userData, context) ??
                false;

        if (success) {
          // Save data locally
          await saveLocally();

          // Trigger cross-screen synchronization
          await _triggerProfileSync();

          // Navigate back with success
          await _handleSuccessfulSave();

          log("Profile updated successfully.");
          return true;
        } else {
          _showErrorMessage("Failed to update profile. Please try again.");
        }
      } catch (e) {
        log('Error saving profile: $e');
        _showErrorMessage("An error occurred while saving. Please try again.");
      } finally {
        if (mounted) {
          setState(() => isSaving = false);
        }
      }
    } else {
      _showValidationErrors();
    }
    return false;
  }

  /// Trigger cross-screen synchronization using ProfileSyncService
  Future<void> _triggerProfileSync() async {
    final syncService = ProfileSyncService();

    // Send profile updated event with new data
    syncService.onProfileUpdated({
      'userName': nameController.text,
      'userLocation': locationController.text,
      'userBio': bioController.text,
      'userProfilePicture': userProfilePicture,
      'userClubInvitation': isNotification,
    });

    log('Profile sync event triggered');
  }

  /// Handle successful save navigation
  Future<void> _handleSuccessfulSave() async {
    if (!mounted) return;

    if (!widget.updateProfile) {
      // Navigate to ProfileScreen for new profile setup
      context.goNamed('ProfileScreen');
    } else {
      // Pop back to previous screen for profile edit
      context.pop(true);
    }
  }

  /// Show validation errors to user
  void _showValidationErrors() {
    setState(() {
      nameValidation = nameController.text.isEmpty;
      locationValidation = locationController.text.isEmpty;
      bioValidation = bioController.text.isEmpty;
    });
  }

  /// Show error message to user
  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppConstants.redColor,
        ),
      );
    }
  }

  Future<void> saveLocally() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    pref.setString('userName', nameController.text);
    pref.setString('userLocation', locationController.text);
    pref.setString('userBio', bioController.text);
    pref.setBool('isUserBioAndLocationAvailable', true);
  }
}
